# 🛒 Nouvelle Facture de Vente - Guide d'Opérations

## 📋 Vue d'ensemble
Ce document décrit toutes les opérations et fonctionnalités de la nouvelle interface "Nouvelle facture de vente" avec son design moderne et ses fonctionnalités avancées.

## 🎯 Structure de l'Interface

### 1. Section Supérieure - LCD et Scanner
- **LCD Display**: Affiche le total final en temps réel
- **Scanner de Code-barres**: Permet l'ajout automatique de produits par scan

### 2. Section Principale - Disposition Côte à Côte
- **Côté Gauche**: Table des articles avec sélection de produits
- **C<PERSON><PERSON> Droit**: Détails de paiement et totaux

## 🔧 Fonctionnalités Détaillées

### Scanner de Code-barres
**Comment ça marche:**
1. Le scanner est automatiquement focalisé à l'ouverture de la facture
2. Scanner ou taper un code-barres
3. Le produit est automatiquement ajouté à la facture
4. Le prix s'affiche instantanément dans le LCD
5. Le stock est automatiquement vérifié

**Ra<PERSON>urcis clavier:**
- **Entrée**: Valide le code-barres scanné
- **Échap**: Efface le champ de scan
- **F1**: Ouvre une nouvelle facture de vente

### LCD Display
**Affichage en temps réel:**
- Total final de la facture
- Mise à jour automatique lors de l'ajout/suppression de produits
- Affichage en format monétaire (DZD)
- Style rétro avec effet lumineux vert

### Sélection de Produits (Côté Gauche)
**En-tête de la table:**
- **Sélectionner un produit**: Menu déroulant avec tous les produits disponibles
- **Quantité**: Champ numérique pour spécifier la quantité
- **Bouton Ajouter**: Ajoute le produit sélectionné à la facture

**Table des articles:**
- Affiche tous les produits ajoutés
- Colonnes: Produit, Quantité, Prix, Total, Action
- Modification de quantité en temps réel
- Bouton de suppression pour chaque article

### Détails de Paiement (Côté Droit)
**Champs principaux:**
1. **Mode de paiement**:
   - Espèces (par défaut)
   - Crédit

2. **Client**:
   - Client de passage (par défaut)
   - Sélection d'un client enregistré
   - Application automatique de remises client

3. **Remise**:
   - Saisie manuelle du montant de remise
   - Calcul automatique des totaux

**Résumé des totaux:**
- Sous-total
- TVA (configurable)
- Remise
- **Total Final** (en surbrillance)

## ⌨️ Raccourcis Clavier

### Raccourcis Globaux
- **F1**: Nouvelle facture de vente
- **F2**: Enregistrer, fermer et imprimer
- **F5**: Actualiser les données
- **Échap**: Fermer la fenêtre

### Raccourcis dans la Facture
- **Entrée**: Enregistrer la facture uniquement
- **Shift + Entrée**: Enregistrer et imprimer thermique
- **F4**: Imprimer la facture (si disponible)

## 🔄 Flux d'Opérations

### 1. Ouverture de Facture
```
Utilisateur clique "Nouvelle facture" ou appuie F1
↓
Modal s'ouvre avec focus automatique sur le scanner
↓
LCD affiche 0.00 DZD
↓
Prêt pour la saisie
```

### 2. Ajout de Produits par Scanner
```
Scanner/taper code-barres
↓
Validation automatique après 500ms
↓
Recherche du produit dans la base
↓
Si trouvé: Ajout automatique + mise à jour LCD
↓
Si non trouvé: Message d'erreur
↓
Champ scanner se vide automatiquement
```

### 3. Ajout de Produits Manuel
```
Sélectionner produit dans la liste
↓
Spécifier quantité (défaut: 1)
↓
Cliquer "Ajouter"
↓
Vérification du stock
↓
Ajout à la table + mise à jour totaux
```

### 4. Modification de Quantité
```
Cliquer sur le champ quantité dans la table
↓
Modifier la valeur
↓
Validation automatique
↓
Vérification du stock disponible
↓
Mise à jour des totaux en temps réel
```

### 5. Sélection du Client
```
Cliquer sur le menu "Client"
↓
Sélectionner un client enregistré
↓
Application automatique de la remise client
↓
Mise à jour des totaux
↓
Message de confirmation de remise
```

### 6. Finalisation de la Facture
```
Vérifier tous les articles
↓
Confirmer le mode de paiement
↓
Ajuster la remise si nécessaire
↓
Cliquer "Enregistrer" ou utiliser raccourci
↓
Validation des données
↓
Sauvegarde + mise à jour stock
↓
Impression automatique (optionnelle)
↓
Fermeture de la modal
```

## ⚠️ Validations et Contrôles

### Validation du Stock
- Vérification automatique avant ajout
- Message d'erreur si stock insuffisant
- Blocage de l'ajout si quantité > stock

### Validation du Client
- Vérification du crédit pour paiement à crédit
- Contrôle de la limite de crédit
- Blocage si limite dépassée

### Validation de la Facture
- Au moins un produit requis
- Client requis pour paiement à crédit
- Montants valides et positifs

## 🎨 Caractéristiques Visuelles

### Design Moderne
- Interface épurée et intuitive
- Couleurs cohérentes avec le thème
- Animations fluides et transitions

### Responsive Design
- Adaptation automatique aux écrans
- Layout vertical sur mobile
- Préservation de la fonctionnalité

### Accessibilité
- Navigation au clavier complète
- Focus visible et logique
- Messages d'erreur clairs

## 🔧 Configuration Technique

### Intégration Scanner
- Support des scanners USB/Bluetooth
- Détection automatique des codes-barres
- Nettoyage automatique des données

### Gestion des États
- Synchronisation en temps réel
- Sauvegarde automatique des modifications
- Récupération en cas d'erreur

### Performance
- Chargement optimisé des produits
- Calculs en temps réel
- Mémoire gérée efficacement

## 📊 Rapports et Suivi

### Données Capturées
- Tous les détails de la facture
- Horodatage précis
- Informations client
- Méthode de paiement

### Intégration Système
- Mise à jour automatique du stock
- Synchronisation avec la comptabilité
- Historique des transactions

## 🚀 Améliorations Futures

### Fonctionnalités Prévues
- Scanner QR codes
- Paiements multiples
- Remises par catégorie
- Impression personnalisée

### Optimisations
- Cache intelligent
- Recherche avancée
- Raccourcis personnalisables
- Thèmes multiples
